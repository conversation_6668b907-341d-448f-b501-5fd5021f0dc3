import {Component, computed, effect, inject, Input, OnInit, signal} from '@angular/core';
import {MaterialsEditorModule} from '../../../../materials-editor/materials-editor.module';
import {SmartCreationMaterialDetail, SmartFieldConfiguration} from '../../../../smart-creation/models/smart-creation.types';
import { ViewModeEnum} from '../../../../materials-editor/models/material-editor.types';
import {TamApePageType} from '../../../../../models';
import {Tam4SelectorConfig, TamAbstractReduxComponent} from '../../../../../components';
import {DeduplicationSelectors} from '../../../store/deduplication.selectors';
import { SelectorMap} from '@creactives/models';
import {TranslateService} from '@ngx-translate/core';
import {Tam4TranslationService} from '@creactives/tam4-translation-core';
import {Store, select} from '@ngrx/store';
import {DeduplicationState} from '../../../store/deduplication.state';
import {DeduplicationService} from '../../../services/deduplication.service';
import {InternalCommonModule} from '../../../../common/iternal-common.module';
import {
  DeduplicationFieldConfiguration,
  DynamicInputConfig,
  EnrichmentTableColumn,
  EnrichmentTableRow
} from '../../../models/deduplication.models';
import {DynamicFormInputFactory} from '../../../../materials-editor/service/dynamic-form-input.factory';
import { FormGroup, Validators} from '@angular/forms';
import {ProgressSpinnerModule} from 'primeng/progressspinner';
import {MessageModule} from 'primeng/message';
import {DocumentData, DynamicComponentWrapper} from '../../../../materials-editor/dynamic-components';
import {ObjectsUtils} from '../../../../../utils';
import {Observable, of} from 'rxjs';
import {
  AlternativeUnitsOfMeasure
} from '../../../../smart-creation/models/smart-creation-validation.types';
import {SmartCreationFormControl} from '../../../../smart-creation/models/smart-creation-form.types';
import {needFlatArray} from '../../../../smart-creation/commons/smart-creation.constants';
import {normalizeControlsFormGroup, setUseTranslatePipe} from '../../../../materials-editor/common/materials-editor.function';
import {SmartCreationDao} from '../../../../smart-creation/smart-creation.dao';
import {SmartCreationDropdownDataRequest} from '../../../../smart-creation/models/smart-creation-validation.types';
import {getCurrentLanguage} from '../../../../layout/store/reducers';
import {getFallbackLanguages} from '../../../../layout/store/profile/profile.state';
import {take, switchMap, catchError} from 'rxjs/operators';
import {RadioButtonModule} from 'primeng/radiobutton';

const storeSelectors: Tam4SelectorConfig[] = [
  {key: 'materials', selector: DeduplicationSelectors.getMaterials},
  {key: 'enrichmentMaterialDetails', selector: DeduplicationSelectors.getEnrichmentMaterialDetails},
];

@Component({
    selector: 'relationship-material-enrich',
    styleUrls: ['./relationship-material-enrich.component.scss'],
    template: `
      <div class="material-enrichment-container">
        <h3>{{ 'deduplication.enrichment.title' | translate }}</h3>

        @if (isLoading()) {
          <p-progressSpinner></p-progressSpinner>
          <p>{{ 'deduplication.enrichment.loading' | translate }}</p>
        } @else if (hasData() && tableRows()?.length > 0 && tableColumns()?.length > 0) {
          <p-table [value]="tableRows()"
                   styleClass="p-datatable-gridlines"
                   [scrollable]="true"
                   scrollHeight="500px"
                   [rowGroupMode]="'subheader'"
                   groupRowsBy="groupTabLabel"
          >
            <ng-template pTemplate="header">
              <tr>
                <th pFrozenColumn></th>
                <th pFrozenColumn>Primary</th>
                @for (column of tableColumns(); track column.field) {
                  @if (!column.isPrimary && column.field !== 'label') {
                    <th>
                      <p-checkbox
                          [binary]="true"
                          [ngModel]="isColumnAllSelected(column)"
                          (onChange)="onColumnSelectAll(column, $event.checked)"
                          [disabled]="!column.materialKey"
                          label="Select All">
                      </p-checkbox>
                    </th>
                  }
                }
              </tr>
            </ng-template>

            <ng-template pTemplate="groupheader" let-rowData>
              <tr class="p-rowgroup-header">
                <td [attr.colspan]="tableColumns().length">
                  <span class="font-bold ml-2">{{ rowData.groupTabLabel }}</span>
                </td>
              </tr>
            </ng-template>
            <ng-template pTemplate="body" let-row>
              <tr>
                @for (column of tableColumns(); track column.field) {
                    @switch (column.field) {
                      @case ('label') {
                        <td pFrozenColumn>
                            <span >{{ row.label | attributeNameTranslate}}</span>
                        </td>
                      }
                      @case ('primary') {
                        <td pFrozenColumn>
                          @if (getPrimaryInputConfig(row.primary); as inputConfig) {
                            @if (inputConfig.component && inputConfig.params && inputConfig?.formControl) {
                              <span scDynamicContainerTemplate
                                    [dynamicComponent]="inputConfig.component"
                                    [dynamicParams]="inputConfig.params"
                                    [editable]="inputConfig.editable && editable"
                                    [viewMode]="type"
                                    [showEmptyValues]="showEmptyValues"
                                    [mandatory]="inputConfig.params?.mandatory"
                                    [coreAttribute]="inputConfig.params?.componentParams?.coreAttribute"
                                    [disableCopy]="true"
                              >
                              </span>
                            }
                          } @else {
                            <span class="raw-value">{{ row.primary.value || '' }}</span>
                          }
                        </td>
                      }
                      @default {
                        <td>
                          @if (column.field.startsWith('secondary_')) {
                            @if (getSecondaryForColumn(row, column); as secondaryField) {
                              <p-checkbox
                                  [ngModel]="isSecondaryFieldSelected(row.fieldId, secondaryField.id, column)"
                                  (ngModelChange)="onSecondaryFieldSelectionChange(row, column, secondaryField, $event)"
                                  label="{{ (secondaryField.value || '') | attributeValueTranslate : secondaryField.id }}"
                                  [name]="secondaryField.id"
                                  [disabled]="!secondaryField.editable"
                                  [binary]="true">
                              </p-checkbox>
                            } @else {
                              <span class="empty-cell"></span>
                            }
                          }
                        </td>
                      }
                    }
                }
              </tr>
            </ng-template>
            <ng-template pTemplate="footer">
              <tr>
                <td colspan="2" pFrozenColumn>

                </td>
                <td colspan="{{ tableColumns().length - 2 }}"></td>
              </tr>
            </ng-template>
          </p-table>
        } @else {
          <p-message severity="info"
                     [text]="'deduplication.enrichment.noData' | translate">
          </p-message>
        }
        <div>
          Hide Non editables
          <p-checkbox
              [binary]="true"
              inputId="nonEditableCheck"
              [ngModel]="hideNonEditable()"
              (ngModelChange)="hideNonEditable.set($event)">
          </p-checkbox>
        </div>
      </div>
    `,
  imports: [
    MaterialsEditorModule,
    InternalCommonModule,
    ProgressSpinnerModule,
    MessageModule,
    RadioButtonModule
  ],
  standalone: true
})
export class RelationshipMaterialEnrich extends TamAbstractReduxComponent<SelectorMap> implements OnInit {

  @Input() mdDomain!: string;

  enrichmentMaterialDetails = computed(() => this.signals?.enrichmentMaterialDetails());
  tableRows = computed(() => this.transformDataToTableRows());
  tableColumns = computed(() => this.generateTableColumns());
  isLoading = computed(() => !this.enrichmentMaterialDetails());
  hasData = computed(() => {
    const data = this.enrichmentMaterialDetails();
    return data && Array.isArray(data) && data.length > 0 &&
           data.some(group => group.rows && group.rows.length > 0);
  });

  hideNonEditable = signal<boolean>(false);

  private dynamicInputConfigCache = new Map<string, DynamicInputConfig | null>();

  disabledAttributes = [
      '4_TAM_UnitOfMeasure',
      '4_TAM_AlternativeUnitOfMeasure',
      '4_SDM_Client'
  ];

  type: ViewModeEnum = ViewModeEnum.EDIT;
  page: string = TamApePageType.EDIT;
  categoriesFormGroup: any;

  service = inject(DeduplicationService);
  dynamicFormInputFactory = inject(DynamicFormInputFactory);
  smartCreationDao = inject(SmartCreationDao);
  materialDetails: SmartCreationMaterialDetail;

  showEmptyValues = true;
  editable = true;
  primaryInputConfigs = new Map<string, DynamicInputConfig | null>();

  constructor(protected translate: TranslateService,
              protected tamTranslate: Tam4TranslationService,
              protected store: Store<DeduplicationState>) {
    super(translate, tamTranslate, store, storeSelectors);

    effect(() => {
      const enrichmentData = this.enrichmentMaterialDetails();
      if (enrichmentData) {
        this.dynamicInputConfigCache.clear();
        console.log('Enrichment data updated:', enrichmentData);
        // console.log('Table rows:', this.tableRows());
        // console.log('Table columns:', this.tableColumns());
      }
    });

    effect(() => {
      const enrichmentData = this.enrichmentMaterialDetails();
      if (enrichmentData) {
        this.dynamicInputConfigCache.clear();
        this.primaryInputConfigs.clear();

        enrichmentData.forEach(group => {
          group.rows?.forEach(rowData => {
            const primaryKeys = Object.keys(rowData.primary || {});
            const primaryField = primaryKeys.length > 0 ? rowData.primary[primaryKeys[0]] : null;
            if (primaryField?.id) {
              const config = this.createDynamicInputConfig(primaryField, primaryField.editable);
              this.primaryInputConfigs.set(primaryField.id, config);
            }
          });
        });

        // console.log('Enrichment data updated:', enrichmentData);
        // console.log('Table rows:', this.tableRows());
        // console.log('Table columns:', this.tableColumns());
      }
    });
  }

  ngOnInit() {
    this.service.loadPrimaryMaterialDetails();
  }

  ngOnDestroy() {
    super.ngOnDestroy();
    this.dynamicInputConfigCache.clear();
  }

  isSecondaryFieldSelected(fieldId: string, secondaryFieldId: string, column: EnrichmentTableColumn): boolean {
    const data = this.enrichmentMaterialDetails();
    if (!data || !Array.isArray(data)) {
      return false;
    }
    for (const group of data) {
      if (group.rows && Array.isArray(group.rows)) {
        for (const row of group.rows) {
          if (row.id === fieldId && row.secondaries) {
            const secondary = row.secondaries[column.materialKey];
            if (secondary.id === secondaryFieldId) {
              return !!secondary.selected;
            }
          }
        }
      }
    }

    return false;
  }

  private transformDataToTableRows(): EnrichmentTableRow[] {
    const data = this.enrichmentMaterialDetails();
    if (!data || !Array.isArray(data)) {
      return [];
    }

    const rows: EnrichmentTableRow[] = [];

    data.forEach(group => {
      if (!group.rows || !Array.isArray(group.rows)) {
        return;
      }

      group.rows.forEach((rowData: any) => {
        const fieldId = rowData.id;

        const primaryKeys = Object.keys(rowData.primary || {});
        const primaryField = primaryKeys.length > 0 ? rowData.primary[primaryKeys[0]] : null;

        if (!primaryField || primaryField.hidden || this.disabledAttributes.includes(primaryField.id) || (this.hideNonEditable() && !primaryField.editable)) {
          return;
        }

        const label = primaryField.descriptionLanguages?.[0] || primaryField.label || fieldId; // TODO: Check this!!!

        const secondaries: DeduplicationFieldConfiguration[] = [];
        if (rowData.secondaries) {
          Object.keys(rowData.secondaries).forEach(secondaryMaterialId => {
            const secondaryField = rowData.secondaries[secondaryMaterialId];
            secondaries.push(secondaryField);
          });
        }
        rows.push({
          fieldId,
          label,
          primary: primaryField,
          secondaries,
          groupTabLabel: group.groupTabLabel,
          groupTabKey: group.groupTabKey
        });
      });
    });

    return rows;
  }

  private generateTableColumns(): EnrichmentTableColumn[] {
    const data = this.enrichmentMaterialDetails();
    if (!data || !Array.isArray(data)) {
      return [];
    }

    const columns: EnrichmentTableColumn[] = [
      { field: 'label', header: 'Field', isPrimary: false },
      { field: 'primary', header: 'Primary', isPrimary: true }
    ];

    // Add secondary columns based on unique secondary material IDs from the new structure
    // Find the first group and first row to get secondary material IDs
    for (const group of data) {
      if (group.rows && group.rows.length > 0) {
        const firstRow = group.rows[0];
        if (firstRow.secondaries) {
          const secondaryMaterialIds = Object.keys(firstRow.secondaries);
          secondaryMaterialIds.forEach((materialId, index) => {
            columns.push({
              field: `secondary_${index}`,
              header: `${materialId}_${index + 1}`,
              isPrimary: false,
              materialKey: materialId
            });
          });
          break; // Only need to process the first row to get column structure
        }
      }
    }

    return columns;
  }

  private createEnhancedParams(originalParams: any, additionalParams: { [key: string]: any } = {}): any {
    return {
      ...originalParams,
      ...additionalParams
    };
  }

  private clearCacheForField(fieldId: string): void {
    const keysToDelete = Array.from(this.dynamicInputConfigCache.keys())
      .filter(key => key.startsWith(`${fieldId}_`));

    keysToDelete.forEach(key => {
      this.dynamicInputConfigCache.delete(key);
    });
  }

  createDynamicInputConfig(fieldConfig: DeduplicationFieldConfiguration, isEditable: boolean): DynamicInputConfig | null {
    try {
      const formGroupInputs = this.initFormGroup(
          [fieldConfig],
          TamApePageType.EDIT,
          ViewModeEnum.EDIT,
          0,
          null,
          this.dynamicAutocompleteFn,
          null,
          null);

      if (!formGroupInputs.items.length || !formGroupInputs.items[0].component || !formGroupInputs.items[0].componentParams) {
        console.warn('Dynamic input config non valido per', fieldConfig);
        return null;
      }

      const formControlKey = fieldConfig.id;
      const formControl = formGroupInputs.formGroup.controls?.[formControlKey] as SmartCreationFormControl;

      if (!formControl) {
        console.warn('Form control not found for field:', formControlKey);
        return null;
      }

      const enhancedParams = this.createEnhancedParams(
          formGroupInputs.items[0].componentParams,
          {
            editable: isEditable
          }
      );

      return {
        component: formGroupInputs.items[0].component,
        params: enhancedParams,
        formControl,
        editable: isEditable
      };
    } catch (error) {
      console.error('Error creating dynamic input config for field:', fieldConfig.id, error);
      return null;
    }
  }

  // TODO: move this to a more appropriate place or reuse existing function in Smart Creation
  private dynamicAutocompleteFnInternal = (source: string,
                                           id: string,
                                           query: string,
                                           documentData: DocumentData,
                                           clientId?: string,
                                           page?: string): Observable<any> => {

    return this.store.pipe(
      select(getCurrentLanguage),
      take(1),
      switchMap(currentLanguage => {
        return this.store.pipe(
          select(getFallbackLanguages),
          take(1),
          switchMap(fallbackLanguages => {
            const enrichmentData = this.enrichmentMaterialDetails();
            let client = clientId;

            // TODO: Check this!!!!
            if (!client && enrichmentData && enrichmentData.length > 0) {
              const firstGroup = enrichmentData[0];
              if (firstGroup.rows && firstGroup.rows.length > 0) {
                const firstRow = firstGroup.rows[0];
                const primaryKeys = Object.keys(firstRow.primary || {});
                if (primaryKeys.length > 0) {
                  const primaryField = firstRow.primary[primaryKeys[0]];
                  client = primaryField?.client;
                }
              }
            }

            const requestBody: SmartCreationDropdownDataRequest = {
              client: client || '',
              language: currentLanguage,
              fallbackLanguages,
              queryText: query,
              categoriesSelected: {},
              materialFormControlList: [],
              domain: '',
              goldenRecordDetails: {},
              completeness: null,
              page: page || this.page,
              processId: null,
              materialId: null
            };

            return this.smartCreationDao.getDropdownData(source, id, requestBody).pipe(
              catchError(error => {
                console.error('Error fetching autocomplete suggestions:', error);
                return of([]);
              })
            );
          })
        );
      })
    );
  }

  /**
   * Wrapper function that matches the expected signature for initFormGroup
   */
  private dynamicAutocompleteFn = (source: string, id: string, documentData: DocumentData): Observable<any> => {
    return this.dynamicAutocompleteFnInternal(source, id, '', documentData);
  }

  private initFormGroup(
      sheets: SmartFieldConfiguration[],
      page: string,
      viewMode: ViewModeEnum,
      sheetIndex: number,
      formGroup?: FormGroup,
      dynamicAutocompleteFn?: (source: string, id: string, documentData: DocumentData) => Observable<any>,
      initialData?: SmartCreationMaterialDetail,
      updateAlternativeUomFn?: (alternativeUomList: AlternativeUnitsOfMeasure[]) => void
  ) {
    const items = [];

    if (!formGroup) {
      formGroup = new FormGroup({});
    }

    sheets?.forEach((smartFieldConfiguration: SmartFieldConfiguration) => {
      formGroup.enable({ onlySelf: true, emitEvent: false });
      if (ObjectsUtils.isNoU(formGroup.controls?.[smartFieldConfiguration.id])) {
        const control: SmartCreationFormControl = this.initFormControl(smartFieldConfiguration);
        formGroup.addControl(smartFieldConfiguration.id, control);
      }

      normalizeControlsFormGroup(formGroup, sheets);
      smartFieldConfiguration.useTranslatePipe = setUseTranslatePipe(smartFieldConfiguration.id);

      const dynamicComponentWrapper: DynamicComponentWrapper = this.componentBasedOnType(
          smartFieldConfiguration,
          page,
          formGroup,
          sheetIndex,
          smartFieldConfiguration.order,
          dynamicAutocompleteFn,
          initialData,
          updateAlternativeUomFn,
          viewMode
      );

      items.push({
        ...dynamicComponentWrapper,
        ...smartFieldConfiguration
      });
    });

    return {
      formGroup,
      items
    };
  }

  getPrimaryInputConfig(fieldConfig: any): DynamicInputConfig | null {
    if (!fieldConfig || !fieldConfig.id) {
      return null;
    }
    return this.primaryInputConfigs.get(fieldConfig.id) || null;
  }

  private componentBasedOnType(v: SmartFieldConfiguration,
                               page: string,
                               formGroup: FormGroup,
                               sheetIndex: number,
                               tabIndex: number,
                               dynamicAutocompleteFn?,
                               initialData?: SmartCreationMaterialDetail,
                               updateAlternativeUomFn?: (alternativeUomList: AlternativeUnitsOfMeasure[]) => void,
                               viewMode?: ViewModeEnum): DynamicComponentWrapper {

    return this.dynamicFormInputFactory.buildDynamicFormInput(v, page, formGroup, sheetIndex, tabIndex, viewMode, initialData, dynamicAutocompleteFn);
  }

  private initFormControl(ctrl: SmartFieldConfiguration): SmartCreationFormControl {
    const value = this.needArrayConversion(ctrl) ? ObjectsUtils.forceOntologyArray(ctrl?.value) : ctrl?.value;
    return new SmartCreationFormControl(value,
        ctrl.mandatory ? [Validators.required] : [],
        null,
        ctrl.label,
        ctrl.dropdownValues && ctrl.dropdownValues.length > 0 ? ctrl.dropdownValues : null,
        'smartCreation.smartValidation.error'
    );
  }

  private needArrayConversion(cfg: any) {
    return needFlatArray(cfg);
  }

  getSecondaryForColumn(row: EnrichmentTableRow, column: EnrichmentTableColumn): DeduplicationFieldConfiguration | null {
    if (!column.field.startsWith('secondary_')) {
      return null;
    }
    const secondaryIndex = parseInt(column.field.replace('secondary_', ''), 10);
    return row.secondaries[secondaryIndex] || null;
  }

  onSecondaryFieldSelectionChange(
      row: EnrichmentTableRow,
      column: EnrichmentTableColumn,
      secondaryField: DeduplicationFieldConfiguration,
      event: any
  ) {
    this.service.updateEnrichmentFieldSelection({
      materialKey: column.materialKey,
      client: '',
      fieldConfiguration: secondaryField
    });
  }

  /**
   * Handle column-wide selection
   */
  onColumnSelectAll(column: EnrichmentTableColumn, selected: boolean) {
    if (column.materialKey) {
      // Clear cache for all fields since bulk selection affects multiple rows
      this.dynamicInputConfigCache.clear();

      this.service.bulkSelectEnrichmentColumn({
        columnMaterialId: column.materialKey,
        selected
      });
    }
  }

  /**
   * Check if all items in a column are selected
   */
  isColumnAllSelected(column: EnrichmentTableColumn): boolean {
    const rows = this.tableRows();
    if (!rows || !column.materialKey) {
      return false;
    }

    const editableRows = rows.filter(row => {
      const secondaryField = this.getSecondaryForColumn(row, column);
      return secondaryField && secondaryField.editable;
    });

    if (editableRows.length === 0) {
      return false;
    }

    return editableRows.every(row => {
      const secondaryField = this.getSecondaryForColumn(row, column);
      return secondaryField && secondaryField.selected;
    });
  }

  /**
   * Check if some items in a column are selected (for indeterminate state)
   */
  isColumnSomeSelected(column: EnrichmentTableColumn): boolean {
    const rows = this.tableRows();
    if (!rows || !column.materialKey) {
      return false;
    }

    const editableRows = rows.filter(row => {
      const secondaryField = this.getSecondaryForColumn(row, column);
      return secondaryField && secondaryField.editable;
    });

    if (editableRows.length === 0) {
      return false;
    }

    const selectedCount = editableRows.filter(row => {
      const secondaryField = this.getSecondaryForColumn(row, column);
      return secondaryField && secondaryField.selected;
    }).length;

    return selectedCount > 0 && selectedCount < editableRows.length;
  }

}
