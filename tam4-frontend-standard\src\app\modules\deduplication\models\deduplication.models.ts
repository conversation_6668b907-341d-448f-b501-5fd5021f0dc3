import { LocalizedFieldValue, MaterialKey} from '@creactives/models';
import { RelationshipRole, RelationshipType } from "../../relationships/endpoint/relationships.model";
import { MaterialPlantChangesRequest } from "../../layout/model/plant.model";
import {StepConfiguration} from '../services/deduplication-step-factory.service';
import {SmartFieldConfiguration} from '../../smart-creation/models/smart-creation.types';
import {SmartCreationFormControl} from '../../smart-creation/models/smart-creation-form.types';

export interface WizardNavigationStep {
  icon: string;
  label: string;
  type: StepType;
  position: 'first' | 'middle' | 'last';
}

export enum StepType {
  SUCCESS = 'success',
  CURRENT = 'current',
  NEXT = 'next',
  ERROR = 'error',
}

export enum StepPage {
  RELATIONSHIP_TYPE,
  MATERIALS_SELECTION,
  MATERIALS_STATUS,
  MATERIAL_ENRICHMENT,
  PLANT_DATA_ENRICHMENT,
  SUMMARY
}

export interface RelationshipMaterialInfo {
  materialId: string;
  client: string;
  materialCode: string;
  completeness: string;
  description: string;
  stockAmount?: number;
  consumptionAmount?: number;
  orderedAmount?: number;
  primary?: boolean;
  materialRelationship?: RelationshipsForMaterial;
  selected?: boolean;
  stockQuantity?: number;
  consumptionQuantity?: number;
  orderedQuantity?: number;
  baseUnitOfMeasurement?: string;
  status?: string;
  statusDescription?: string;
  obsolete?: boolean;
  isTopStockValue?: boolean;
  isTopConsumptionValue?: boolean;
  isTopOrderedValue?: boolean;
  isGoldenRecord?: boolean;
}

export interface RelationshipsForMaterial {
  materialId: string;
  materialRelationshipsDetails: Array<MaterialDeduplicationDetails>;
}

export interface MaterialDeduplicationDetails {
  relationshipId: string;
  relationshipRole: RelationshipRole;
  relationshipType: RelationshipType;
}

export interface DeduplicationMaterialData {
  materials: Array<RelationshipMaterialInfo>;
}

export interface DeduplicationValidateDraft {
  materialId: string;
  materialCode: string;
  client: string;
  errorMessage: string;
}

export interface ClientStatuses {
  [client: string]: LocalizedFieldValue[];
}

export interface ClientMultiMap {
  [client: string]: RelationshipMaterialInfo[];
}

export interface SubtypesRelationships {
  relationshipType: RelationshipType;
  subtypes: Subtype[];
}

export interface Subtype {
  key: string;
  value: string;
}

export interface MaterialIdsRequest {
  materialIds: string[];
  language: string;
  fallbackLanguages: string[];
}

export interface MaterialInRelationship {
  materialId: string;
  role: string;
  materialStatus?: string;
  primary: boolean;
  selected: boolean;
  status?: string;
}

export interface MaterialInRelationshipBE {
  materialId: string;
  role: string;
  materialStatus?: string;
}

export interface CreateDeduplicationRequestBE {
  relationshipType: RelationshipType;
  materialsInRelationship: Array<MaterialInRelationshipBE>;
  note: string;
  parentProcessId?: string;
  creatingGoldenRecord?: boolean;
}

export interface CreateDeduplicationRequest {
  relationshipType?: RelationshipType;
  subtype?: Subtype;
  materialsInRelationship: Array<MaterialInRelationship>;
  materialsDeduplication: RelationshipMaterialInfo[];
  note?: string;
  parentProcessId?: string;
  creatingGoldenRecord?: boolean;
  invalidField?: string[];
  isCreatingGoldenRecord?: boolean;
  materialStatus?: string;
  selected?: boolean;
  status?: string;
  plantChanges?: any; //TODO DA SOSTITUIRE CON LA PROPERTY ADATTA
}

export interface DeduplicationValidateDraftResponse {
  crossClient: boolean;
  relationshipValidateDrafts: Array<DeduplicationValidateDraft>;
  clientValidation: ClientErrors;
}

export interface ClientErrors {
  [client: string]: string;
}

export interface ValidationErrors {
    crossClient: boolean;
    materials?: Array<DeduplicationValidateDraft>;
    client?: ClientErrors;
    hasErrors: boolean;
    errors?: string[];
}

export interface DeduplicationStep {
  stepPage: number;
  isValid: boolean;
  errors?: string[];
  stepConfiguration?: StepConfiguration;
}

// TODO: in questa variabile estrarre i campi di deduplication deduplicationBasicDataEnrichment, deduplicationPlantEnrichment, deduplicationSubTypesEnabled

export interface LicenseConfigurations {
  deduplicationBasicDataEnrichment: boolean; // TODO: cambiare in deduplicationMasterDataEnrichment
  deduplicationPlantEnrichment: boolean;
  deduplicationSubTypesEnabled: boolean;
  deduplicationMasterDataStatusEnabled: boolean;
  deduplicationMasterDataStatusRequired: boolean;
}

export interface DeduplicationRequest extends CreateDeduplicationRequestBE{
  data?: DeduplicationData[];
  subtype?: Subtype;
}

export interface DeduplicationData {
  client?: string;
  primary?: DeduplicationMaterialDetailsEdit;
  secondaries?: MaterialInRelationship[]; //MANCANO MATERIALCODE E IL CLIENT PRESENTI NELLA REQUEST BE
  plantExtensionsRequest?: any //MAPPARE OGGETTO QUANDO PRESENTE
  plantChangesRequests?: MaterialPlantChangesRequest[];
}

export interface DeduplicationMaterialDetailsEdit {
  details?: any;
  primaryMaterialStatus?: string;
}

export interface DeduplicationBasicDataEnrichmentRequest {
  primaryMaterialKey: MaterialKey;
  secondariesMaterialCodes: string[];
  language: string;
  fallbackLanguages: string[];
}

export interface DataRow {
  [key: string]: DeduplicationFieldConfiguration;
}

export interface DeduplicationEnrichedDataDetails {
  groupTabLabel?: string;
  groupTabKey?: string;
  rows: DeduplicationBasicDataRowDto[];
}

export interface DeduplicationBasicDataRowDto {
  id: string;
  secondaries: { [key: string]: DeduplicationFieldConfiguration };
  primary: { [key: string]: DeduplicationFieldConfiguration };
}

export interface DeduplicationFieldConfiguration extends SmartFieldConfiguration {
  value: any;
  oldValue?: any;
  selected?: boolean;
}

export interface EnrichmentTableRow {
  fieldId: string;
  label: string;
  primary: DeduplicationFieldConfiguration;
  secondaries: DeduplicationFieldConfiguration[];
  groupTabLabel?: string;
  groupTabKey?: string;
}

export interface EnrichmentTableColumn {
  field: string;
  header: string;
  isPrimary: boolean;
  materialKey?: string;
}

export interface DynamicInputConfig {
  component: any;
  params: any;
  formControl: SmartCreationFormControl;
  editable: boolean;
}

